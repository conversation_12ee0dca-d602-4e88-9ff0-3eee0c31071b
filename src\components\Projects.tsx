
import { motion } from "framer-motion";
import ProjectsGrid from "./ProjectsGrid";

const Projects = () => {
  return (
    <section id="projects" className="section-padding bg-ouroboros-background">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <h2 className="text-3xl md:text-4xl mb-6 font-trajan text-ouroboros-accent">
            Projecten
          </h2>

          <p className="text-lg leading-relaxed text-ouroboros-accent/80 max-w-3xl mb-8">
            Ontdek onze diverse projecten en producties. Elk project vertelt een uniek verhaal.
          </p>
        </motion.div>

        {/* Featured projects grid */}
        <ProjectsGrid />
      </div>
    </section>
  );
};

export default Projects;
