
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { Send } from "lucide-react";
import emailjs from '@emailjs/browser';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldFocus, setFieldFocus] = useState({
    name: false,
    email: false,
    message: false
  });

  const { toast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFocus = (field: string) => {
    setFieldFocus(prev => ({ ...prev, [field]: true }));
  };

  const handleBlur = (field: string) => {
    setFieldFocus(prev => ({ ...prev, [field]: false }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Replace these with your actual EmailJS credentials
      const serviceId = 'service_7e7crw7';
      const templateId = 'template_y70c1ym';
      const publicKey = '5_4DeoJ7jJAniUDFc';

      const templateParams = {
        to_name: 'Maarten Minne',
        to_email: '<EMAIL>',
        sender_name: formData.name,
        sender_email: formData.email,
        message: formData.message,
        subject: 'Nieuw contactformulier bericht van website'
      };

      await emailjs.send(serviceId, templateId, templateParams, publicKey);

      toast({
        title: "Bericht verstuurd",
        description: "Bedankt voor je bericht. We nemen zo snel mogelijk contact met je op.",
        duration: 5000,
      });

      setFormData({ name: "", email: "", message: "" });
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: "Fout bij verzenden",
        description: "Er is iets misgegaan bij het verzenden van je bericht. Probeer het later opnieuw.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="section-padding bg-ouroboros-background">
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 lg:gap-24 px-4 sm:px-6 md:px-8">
        {/* Contact Info */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div>
            <h2 className="text-3xl md:text-4xl mb-6 font-trajan text-ouroboros-accent">
              Samenwerken?
            </h2>

            <p className="text-lg text-ouroboros-accent/80 mb-8 leading-relaxed">
              Heb je een idee of creatie in gedachten? Wij helpen je graag om jouw verhaal tot leven te brengen.
              Neem contact met ons op en laat ons samen iets bijzonders creëren.
            </p>
          </div>

          <div className="space-y-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="min-w-[3rem] w-12 h-12 rounded-full bg-ouroboros-accent/10 flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-ouroboros-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-semibold text-ouroboros-accent/60">E-mail</h4>
                <a href="mailto:<EMAIL>" className="text-ouroboros-accent hover:underline transition break-words"><EMAIL></a>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-4">
              <div className="min-w-[3rem] w-12 h-12 rounded-full bg-ouroboros-accent/10 flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-ouroboros-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-semibold text-ouroboros-accent/60">Telefoon</h4>
                <p className="not-italic text-ouroboros-accent">+32 499 96 85 96</p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-trajan mb-4 text-ouroboros-accent">Volg ons</h4>
            <div className="flex flex-wrap gap-4">
              <a href="#" className="min-w-[2.5rem] w-10 h-10 rounded-full bg-ouroboros-accent/10 flex items-center justify-center text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors flex-shrink-0">
                <span className="sr-only">Instagram</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                </svg>
              </a>
              <a href="#" className="min-w-[2.5rem] w-10 h-10 rounded-full bg-ouroboros-accent/10 flex items-center justify-center text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors flex-shrink-0">
                <span className="sr-only">Facebook</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                </svg>
              </a>
              <a href="#" className="min-w-[2.5rem] w-10 h-10 rounded-full bg-ouroboros-accent/10 flex items-center justify-center text-ouroboros-accent hover:bg-ouroboros-accent hover:text-white transition-colors flex-shrink-0">
                <span className="sr-only">LinkedIn</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                </svg>
              </a>
            </div>
          </div>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          viewport={{ once: true }}
          className="space-y-4 w-full"
        >
          <form onSubmit={handleSubmit} className="space-y-8 w-full">
            <motion.div
              className="space-y-2"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Label htmlFor="name" className="text-ouroboros-accent/80 font-medium">
                Naam <span className="text-ouroboros-accent">*</span>
              </Label>
              <Input
                id="name"
                placeholder="Jouw naam"
                name="name"
                value={formData.name}
                onChange={handleChange}
                onFocus={() => handleFocus('name')}
                onBlur={() => handleBlur('name')}
                required
                className={`w-full border-ouroboros-accent/20 bg-transparent h-12 px-4
                           focus-visible:ring-1 focus-visible:ring-ouroboros-accent
                           hover:border-ouroboros-accent/50 transition-all duration-200
                           placeholder:text-ouroboros-accent/40
                           ${fieldFocus.name ? 'border-ouroboros-accent ring-1 ring-ouroboros-accent' : ''}`}
              />
            </motion.div>

            <motion.div
              className="space-y-2"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <Label htmlFor="email" className="text-ouroboros-accent/80 font-medium">
                E-mail <span className="text-ouroboros-accent">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                name="email"
                value={formData.email}
                onChange={handleChange}
                onFocus={() => handleFocus('email')}
                onBlur={() => handleBlur('email')}
                required
                className={`w-full border-ouroboros-accent/20 bg-transparent h-12 px-4
                           focus-visible:ring-1 focus-visible:ring-ouroboros-accent
                           hover:border-ouroboros-accent/50 transition-all duration-200
                           placeholder:text-ouroboros-accent/40
                           ${fieldFocus.email ? 'border-ouroboros-accent ring-1 ring-ouroboros-accent' : ''}`}
              />
            </motion.div>

            <motion.div
              className="space-y-2"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <Label htmlFor="message" className="text-ouroboros-accent/80 font-medium">
                Bericht <span className="text-ouroboros-accent">*</span>
              </Label>
              <Textarea
                id="message"
                placeholder="Jouw bericht..."
                name="message"
                value={formData.message}
                onChange={handleChange}
                onFocus={() => handleFocus('message')}
                onBlur={() => handleBlur('message')}
                required
                rows={5}
                className={`w-full border-ouroboros-accent/20 bg-transparent min-h-[150px] px-4 py-3
                           focus-visible:ring-1 focus-visible:ring-ouroboros-accent
                           hover:border-ouroboros-accent/50 transition-all duration-200
                           placeholder:text-ouroboros-accent/40 resize-y
                           ${fieldFocus.message ? 'border-ouroboros-accent ring-1 ring-ouroboros-accent' : ''}`}
              />
            </motion.div>

            <motion.div
              className="pt-2 flex justify-end"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              viewport={{ once: true }}
            >
              <Button
                type="submit"
                size="lg"
                disabled={isSubmitting}
                className="bg-ouroboros-accent hover:bg-ouroboros-accent/90 text-white font-medium
                           transition-all duration-300 hover:shadow-md relative overflow-hidden
                           group"
              >
                <span className={`inline-flex items-center gap-2 transition-opacity duration-200 ${isSubmitting ? 'opacity-0' : 'opacity-100'}`}>
                  Versturen
                  <Send className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                </span>
                {isSubmitting && (
                  <span className="absolute inset-0 flex items-center justify-center">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                )}
              </Button>
            </motion.div>
          </form>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
