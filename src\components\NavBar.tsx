
import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

const NavBar = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const isHomePage = location.pathname === '/';
  const isProjectDetailPage = location.pathname.includes('/projects/');

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();

    // Close mobile menu if open
    setMobileMenuOpen(false);

    // If we're not on the homepage, navigate to homepage first
    if (!isHomePage) {
      window.location.href = '/#' + id;
      return;
    }

    // If we're already on the homepage, just scroll
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  const textColor = isProjectDetailPage ? "text-ouroboros-accent" :
                    scrolled ? "text-ouroboros-accent" : "text-white";

  return (
    <nav
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300 px-8 py-4",
        {
          // On project detail pages - always white
          "bg-white border-b border-gray-200": isProjectDetailPage,
          // On other pages with scroll
          "bg-[#f5f5f4] border-b border-gray-200": !isProjectDetailPage && scrolled,
          // On other pages without scroll
          "bg-transparent border-b border-transparent": !isProjectDetailPage && !scrolled
        }
      )}
    >
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        {isHomePage ? (
          <a
            href="#"
            className="transition-opacity duration-300 hover:opacity-90 py-1"
            onClick={(e) => handleSmoothScroll(e, 'hero')}
          >
            <img
              src={
                isProjectDetailPage ||
                (!isProjectDetailPage && scrolled)
                  ? "/logo/logo_long_blue_full.svg"
                  : "/logo/logo_long_white_full.svg"
              }
              alt="Ouroboros Productiehuis"
              className="h-8 mt-1 mb-1 w-auto"
            />
          </a>
        ) : (
          <Link
            to="/"
            className="transition-opacity duration-300 hover:opacity-90 py-1"
          >
            <img
              src={
                isProjectDetailPage ||
                (!isProjectDetailPage && scrolled)
                  ? "/logo/logo_long_blue_full.svg"
                  : "/logo/logo_long_white_full.svg"
              }
              alt="Ouroboros Productiehuis"
              className="h-8 mt-1 mb-1 w-auto"
            />
          </Link>
        )}

        {/* Desktop Navigation */}
        <div className="hidden md:flex space-x-8">
          <a
            href="#about"
            className={cn(
              "hover:opacity-70 transition-opacity",
              textColor
            )}
            onClick={(e) => handleSmoothScroll(e, 'about')}
          >
            Over ons
          </a>
          {isHomePage ? (
            <a
              href="#projects"
              className={cn(
                "hover:opacity-70 transition-opacity",
                textColor
              )}
              onClick={(e) => handleSmoothScroll(e, 'projects')}
            >
              Projecten
            </a>
          ) : (
            <Link
              to="/#projects"
              className={cn(
                "hover:opacity-70 transition-opacity",
                textColor
              )}
            >
              Projecten
            </Link>
          )}
          <a
            href="#contact"
            className={cn(
              "hover:opacity-70 transition-opacity",
              textColor
            )}
            onClick={(e) => handleSmoothScroll(e, 'contact')}
          >
            Contact
          </a>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden">
          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
            <SheetTrigger asChild>
              <button
                className={cn(
                  "p-2 rounded-md transition-all duration-300 focus:outline-none",
                  textColor
                )}
                aria-label="Menu"
              >
                {mobileMenuOpen ? (
                  <X className="h-6 w-6 text-white" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className={cn(
                "w-full sm:w-80 p-0 flex flex-col bg-ouroboros-accent [&>button]:text-white [&>button]:hover:bg-ouroboros-accent/80"
              )}
            >
              <div className="flex flex-col h-full justify-center items-center space-y-8 py-12">
                <a
                  href="#about"
                  className="text-xl font-medium text-white hover:opacity-70 transition-opacity"
                  onClick={(e) => handleSmoothScroll(e, 'about')}
                >
                  Over ons
                </a>
                {isHomePage ? (
                  <a
                    href="#projects"
                    className="text-xl font-medium text-white hover:opacity-70 transition-opacity"
                    onClick={(e) => handleSmoothScroll(e, 'projects')}
                  >
                    Projecten
                  </a>
                ) : (
                  <Link
                    to="/#projects"
                    className="text-xl font-medium text-white hover:opacity-70 transition-opacity"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Projecten
                  </Link>
                )}
                <a
                  href="#contact"
                  className="text-xl font-medium text-white hover:opacity-70 transition-opacity"
                  onClick={(e) => handleSmoothScroll(e, 'contact')}
                >
                  Contact
                </a>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
};

export default NavBar;
