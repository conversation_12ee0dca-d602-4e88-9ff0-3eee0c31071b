
import { useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import { useProjects } from '@/contexts/ProjectContext';
import { ArrowRight, Users } from 'lucide-react';

const ProjectsGrid = () => {
  const { projects } = useProjects();
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(gridRef, { once: true, amount: 0.1 });

  // Create a staggered animation effect for projects
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: [0.22, 1, 0.36, 1] }
    }
  };

  return (
    <div>
      {/* Projects grid */}
      <div ref={gridRef} className="relative">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8"
        >
          {projects.map((project) => {
            const isHovered = hoveredProject === project.id;

            return (
              <motion.div
                key={project.id}
                variants={itemVariants}
                className="group relative overflow-hidden rounded-xl aspect-square shadow-md border border-ouroboros-accent/10"
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                <Link to={`/projects/${project.id}`} className="block w-full h-full">
                  {/* Background image with overlay */}
                  <div className="absolute inset-0 w-full h-full">
                    <img
                      src={project.imageUrl}
                      alt={project.title}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-ouroboros-accent/90 via-ouroboros-accent/40 to-transparent opacity-80 group-hover:opacity-90 transition-opacity duration-500" />
                  </div>

                  {/* Content overlay */}
                  <div className="absolute inset-0 p-5 flex flex-col justify-between text-white z-10">
                    {/* Top section with project info */}
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <motion.h3
                          className="text-lg md:text-xl font-trajan"
                          initial={{ opacity: 0.9, y: 0 }}
                          animate={{
                            opacity: isHovered ? 1 : 0.9,
                            y: isHovered ? -5 : 0,
                            transition: { duration: 0.3 }
                          }}
                        >
                          {project.title}
                        </motion.h3>

                        <AnimatePresence>
                          {isHovered && (
                            <motion.p
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="text-xs md:text-sm text-white/90 line-clamp-2"
                            >
                              {project.tagline || project.description.replace(/\n/g, ' ').substring(0, 80) + '...'}
                            </motion.p>
                          )}
                        </AnimatePresence>
                      </div>

                     
                    </div>

                    {/* Bottom section with view button */}
                    <div className="flex justify-end items-end">
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{
                          opacity: isHovered ? 1 : 0,
                          x: isHovered ? 0 : -10
                        }}
                        transition={{ duration: 0.3 }}
                        className="flex items-center"
                      >
                        <span className="text-sm font-medium mr-2">Bekijk project</span>
                        <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                      </motion.div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </div>
  );
};

export default ProjectsGrid;
